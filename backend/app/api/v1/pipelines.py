"""
Pipeline API Endpoints

This module provides REST API endpoints for triggering data enrichment pipelines.
It forwards requests to the datapipelines service running on port 8001.
"""

import httpx
from typing import Any, Dict, List, Optional
from fastapi import Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.user import User
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/pipelines", tags=["pipelines"])


class PipelineTriggerRequest(BaseModel):
    """Request model for triggering a pipeline."""

    company_id: str = Field(..., description="Company identifier (website URL)")
    org_id: str = Field(..., description="Organization identifier")
    company_name: str = Field(..., description="Company name")
    domain: Optional[str] = Field(None, description="Company domain")
    form_data: Optional[Dict[str, Any]] = Field(
        None, description="Form submission data"
    )
    pipeline_types: List[str] = Field(
        default=["company", "founder", "news", "embedding"],
        description="Types of pipelines to run",
    )
    priority: str = Field(
        default="normal", description="Job priority (low, normal, high)"
    )


class PipelineTriggerResponse(BaseModel):
    """Response model for pipeline trigger."""

    success: bool = Field(..., description="Whether the trigger was successful")
    message: str = Field(..., description="Response message")
    job_ids: Optional[Dict[str, str]] = Field(None, description="Job IDs for each pipeline type")


@router.post("/trigger", response_model=PipelineTriggerResponse)
@rbac_register(
    resource="pipeline", action="create", group="Pipelines", description="Trigger pipeline"
)
async def trigger_pipeline(
    request: PipelineTriggerRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
) -> PipelineTriggerResponse:
    """
    Trigger founder enrichment pipeline.
    
    This endpoint forwards enrichment requests to the datapipelines service
    running on port 8001. It's used to trigger founder data processing
    for companies.
    
    Args:
        request: Pipeline trigger request with company and founder data
        
    Returns:
        Response indicating success/failure and any job IDs
    """
    try:
        _, _ = org_context  # Extract org context for potential future use
        logger.info(
            f"Triggering pipeline for company {request.company_name} "
            f"with types {request.pipeline_types}"
        )
        
        # Forward request to datapipelines service on port 8001
        datapipelines_url = "http://localhost:8001/api/v1/pipelines/trigger"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                datapipelines_url,
                json=request.model_dump(),
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"Pipeline triggered successfully: {response_data}")
                
                return PipelineTriggerResponse(
                    success=True,
                    message="Pipeline triggered successfully",
                    job_ids=response_data.get("job_ids", {})
                )
            else:
                logger.error(f"Pipeline trigger failed: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=f"Pipeline service error: {response.text}"
                )
                
    except httpx.TimeoutException:
        logger.error("Pipeline service timeout")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Pipeline service timeout"
        )
    except httpx.ConnectError:
        logger.error("Cannot connect to pipeline service")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Pipeline service unavailable"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering pipeline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
