"use client"

import { useState, useEffect, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import { Shell } from '@/components/shell'
import { DealsHeader } from '@/components/core/deals/deals-header'
import { DealsGrid } from '@/components/core/deals/deals-grid'
import { NewDealModal } from '@/components/core/deals/new-deal-modal'
import { DealAPI } from '@/lib/api/deal-api'
import { Deal, DealStatus, DealDashboardFilters } from '@/lib/types/deal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/components/ui/use-toast'

export default function DealsPage() {
  const { isAuthenticated, loading, user } = useAuth()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // State management
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([])
  const [error, setError] = useState<string | null>(null)
  const [showNewDealModal, setShowNewDealModal] = useState(false)

  // Parse filters from URL
  const currentFilters = useMemo(() => {
    const filters: DealDashboardFilters = {
      status: ['new', 'triage', 'reviewed'],
      assigned_to_me: true,
      sort_by: 'updated_at',
      sort_dir: 'desc'
    }

    // Parse URL parameters
    const status = searchParams?.get('status')
    if (status) {
      filters.status = status.split(',')
    }
    const assignedToMe = searchParams?.get('assigned_to_me')
    if (assignedToMe) {
      filters.assigned_to_me = assignedToMe === 'true'
    }
    const createdAtStart = searchParams?.get('created_at_start')
    if (createdAtStart) {
      filters.created_at_start = createdAtStart
    }
    const createdAtEnd = searchParams?.get('created_at_end')
    if (createdAtEnd) {
      filters.created_at_end = createdAtEnd
    }
    const sortBy = searchParams?.get('sort_by')
    if (sortBy) {
      filters.sort_by = sortBy
    }
    const sortDir = searchParams?.get('sort_dir')
    if (sortDir) {
      filters.sort_dir = sortDir
    }
    const favouritesOnly = searchParams?.get('favourites_only')
    if (favouritesOnly) {
      filters.favourites_only = favouritesOnly === 'true'
    }

    return filters
  }, [searchParams])

  // Fetch deals with current filters
  const fetchDeals = async (filters?: DealDashboardFilters) => {
    try {
      setError(null)

      const response = await DealAPI.listDeals(0, 1000, filters || currentFilters)
      setAllDeals(response.deals || [])
      setFilteredDeals(response.deals || [])
    } catch (err: any) {
      console.error('Error fetching deals:', err)
      setError('Failed to load deals. Please try again.')
      toast({
        title: "Error",
        description: "Failed to load deals. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Fetch deals when filters change
  useEffect(() => {
    if (isAuthenticated) {
      fetchDeals(currentFilters)
    }
  }, [isAuthenticated, currentFilters])

  // Calculate deal counts for filters
  const dealCounts = useMemo(() => ({
    all: allDeals.length,
    new: allDeals.filter(d => d.status === DealStatus.NEW).length,
    triage: allDeals.filter(d => d.status === DealStatus.TRIAGE).length,
    reviewed: allDeals.filter(d => d.status === DealStatus.REVIEWED).length,
    approved: allDeals.filter(d => d.status === DealStatus.APPROVED).length,
    excluded: allDeals.filter(d => d.status === DealStatus.EXCLUDED).length,
    closed: allDeals.filter(d => d.status === DealStatus.CLOSED).length,
  }), [allDeals])

  const handleNewDeal = () => {
    setShowNewDealModal(true)
  }

  const handleDeleteDeal = async (dealId: string): Promise<boolean> => {
    try {
      await DealAPI.deleteDeal(dealId)
      
      // Remove from local state
      setAllDeals(prev => prev.filter(d => d.id !== dealId && d._id !== dealId))
      setFilteredDeals(prev => prev.filter(d => d.id !== dealId && d._id !== dealId))
      
      toast({
        title: "Deal deleted",
        description: "Deal has been deleted successfully.",
      })
      
      return true
    } catch (error: any) {
      console.error('Error deleting deal:', error)
      toast({
        title: "Error",
        description: "Failed to delete deal. Please try again.",
        variant: "destructive",
      })
      return false
    }
  }

  const handleSearchChange = (search: string) => {
    if (!search.trim()) {
      setFilteredDeals(allDeals)
      return
    }

    const searchLower = search.toLowerCase()
    const filtered = allDeals.filter(deal => 
      deal.company_name?.toLowerCase().includes(searchLower) ||
      deal.company_website?.toLowerCase().includes(searchLower) ||
      deal.sector?.toString().toLowerCase().includes(searchLower) ||
      deal.stage?.toLowerCase().includes(searchLower) ||
      deal.notes?.toLowerCase().includes(searchLower)
    )
    setFilteredDeals(filtered)
  }

  const handleFilterChange = (filters: DealDashboardFilters) => {
    // The filters are already applied via URL params, so we just need to refetch
    fetchDeals(filters)
  }

  return (
    <Shell>
      <div className="space-y-8">
        {/* Show loading while checking authentication */}
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Checking authentication...</span>
          </div>
        ) : !isAuthenticated ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <p className="text-muted-foreground">Redirecting to login...</p>
            </div>
          </div>
        ) : (
          <>
            <DealsHeader
              onSearchChange={handleSearchChange}
              onFilterChange={handleFilterChange}
              totalDeals={filteredDeals.length}
              dealCounts={dealCounts}
              loading={loading}
            />

            <DealsGrid
              deals={filteredDeals}
              loading={loading}
              error={error}
              onNewDeal={handleNewDeal}
              onDeleteDeal={handleDeleteDeal}
            />

            {/* New Deal Modal */}
            <NewDealModal
              open={showNewDealModal}
              onOpenChange={setShowNewDealModal}
            />
          </>
        )}
      </div>
    </Shell>
  )
} 