"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Briefcase, MapPin, Calendar } from "lucide-react"
import { FounderExperience } from "@/lib/api/founder-api"

interface FounderTimelineProps {
  experiences: FounderExperience[]
  className?: string
}

export function FounderTimeline({ experiences, className }: FounderTimelineProps) {
  // Sort experiences by start date (most recent first)
  const sortedExperiences = [...experiences].sort((a, b) => {
    const dateA = new Date(a.startDate).getTime()
    const dateB = new Date(b.startDate).getTime()
    return dateB - dateA
  })

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      })
    } catch {
      return dateString
    }
  }

  const calculateDuration = (startDate: string, endDate?: string) => {
    try {
      const start = new Date(startDate)
      const end = endDate ? new Date(endDate) : new Date()
      const diffTime = Math.abs(end.getTime() - start.getTime())
      const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))
      
      if (diffMonths < 12) {
        return `${diffMonths} month${diffMonths !== 1 ? 's' : ''}`
      } else {
        const years = Math.floor(diffMonths / 12)
        const remainingMonths = diffMonths % 12
        if (remainingMonths === 0) {
          return `${years} year${years !== 1 ? 's' : ''}`
        }
        return `${years}y ${remainingMonths}m`
      }
    } catch {
      return 'Unknown duration'
    }
  }

  if (sortedExperiences.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Experience Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No experience data available</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Briefcase className="h-4 w-4" />
          Experience Timeline
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-4 top-0 bottom-0 w-px bg-border" />
          
          <div className="space-y-6">
            {sortedExperiences.map((experience, index) => (
              <div key={experience.id} className="relative flex gap-4">
                {/* Timeline dot */}
                <div className="relative z-10 flex h-8 w-8 items-center justify-center">
                  <div className="h-3 w-3 rounded-full bg-primary border-2 border-background shadow-sm" />
                </div>
                
                {/* Experience content */}
                <div className="flex-1 min-w-0 pb-4">
                  <div className="space-y-2">
                    <div>
                      <h4 className="font-medium text-sm leading-tight">
                        {experience.position}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {experience.companyName}
                      </p>
                    </div>
                    
                    <div className="flex flex-wrap items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {formatDate(experience.startDate)} - {' '}
                          {experience.endDate ? formatDate(experience.endDate) : 'Present'}
                        </span>
                      </div>
                      
                      <Badge variant="secondary" className="text-xs">
                        {calculateDuration(experience.startDate, experience.endDate)}
                      </Badge>
                      
                      {experience.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          <span>{experience.location}</span>
                        </div>
                      )}
                    </div>
                    
                    {experience.industry && (
                      <Badge variant="outline" className="text-xs w-fit">
                        {experience.industry}
                      </Badge>
                    )}
                    
                    {experience.companySize && (
                      <p className="text-xs text-muted-foreground">
                        Company size: {experience.companySize}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
