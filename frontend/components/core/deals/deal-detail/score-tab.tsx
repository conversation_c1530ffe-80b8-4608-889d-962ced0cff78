"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Target,
  TrendingUp,
  TrendingDown,
  ChevronRight,
  BarChart3,
  Calendar,
  Shield
} from "lucide-react"
import { cn } from "@/lib/utils"
import { mobileRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Deal } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { ExclusionDisplay } from "./exclusion-display"
import { FormSubmissionRequest } from "./form-submission-request"

interface ScoreTabProps {
  deal: DealDetailData | Deal
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50'
  if (score >= 40) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

const getMatchStatusBadge = (score: number) => {
  if (score >= 80) return { text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200' }
  if (score >= 60) return { text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200' }
  if (score >= 40) return { text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
  return { text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200' }
}

export function ScoreTab({ deal }: ScoreTabProps) {
  const [animatedScore, setAnimatedScore] = useState(0)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Extract scoring data from the correct structure
  const dealScoring = (deal as any).scoring
  const thesisScoring = dealScoring?.thesis
  const exclusionResult = (deal as any).exclusion_filter_result
  const isExcluded = exclusionResult?.excluded || false

  // Calculate key metrics from the actual backend data structure
  const thesisMatchPercent = thesisScoring?.score?.normalized_percent ? Math.round(thesisScoring.score.normalized_percent) : null
  const coreScore = thesisScoring?.score?.core || 0
  const bonusTotal = thesisScoring?.score?.bonus || 0
  const penaltyTotal = thesisScoring?.score?.penalty || 0
  const maxPossibleScore = thesisScoring?.score?.max_possible || 100
  const lastScored = thesisScoring?.last_scored_at
  const thesisName = thesisScoring?.thesis_name || 'Investment Thesis'

  // Check if we should show form submission request
  const hasSubmissions = (deal.submission_ids && deal.submission_ids.length > 0) || false
  const inviteStatus = deal.invite_status || null
  const shouldShowFormRequest = !hasSubmissions && inviteStatus !== 'sent'

  // Animate score on mount
  useEffect(() => {
    if (thesisMatchPercent !== null) {
      const timer = setTimeout(() => {
        setAnimatedScore(thesisMatchPercent)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [thesisMatchPercent])

  const handleViewFullAnalysis = () => {
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  const handleFormRequestSuccess = () => {
    // Trigger a refresh of the component to update the UI
    setRefreshTrigger(prev => prev + 1)
  }

  // Show exclusion first if deal is excluded
  if (isExcluded) {
    return (
      <div className="space-y-6">
        <ExclusionDisplay exclusionResult={exclusionResult} />
        {thesisScoring && (
          <Card className="border-0 shadow-sm bg-gray-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg text-gray-600">
                <BarChart3 className="h-5 w-5" />
                Scoring Data (Hidden due to exclusion)
              </CardTitle>
              <p className="text-sm text-gray-500">
                This deal was scored but results are hidden due to exclusion filter.
              </p>
            </CardHeader>
          </Card>
        )}
      </div>
    )
  }

  // Show form submission request if no submissions and invite not sent
  if (shouldShowFormRequest) {
    return (
      <div className="space-y-6">
        <FormSubmissionRequest 
          deal={deal} 
          onSuccess={handleFormRequestSuccess}
        />
      </div>
    )
  }

  // Show empty state if no scoring data but form request was sent
  if (!thesisScoring || thesisMatchPercent === null) {
    return (
      <div className="space-y-6">
        <div className={cn(mobileRetreat.empty.container, "py-16")}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-center"
          >
            <div className={mobileRetreat.empty.icon}>
              <Target className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-6 text-gray-400" />
            </div>
            <h3 className={mobileRetreat.empty.title}>No Scoring Data Available</h3>
            <p className={mobileRetreat.empty.description}>
              {inviteStatus === 'sent' 
                ? "Form request has been sent. Scoring will appear here once the founder completes the submission."
                : "This deal hasn't been scored yet. Scoring will appear here once the analysis is complete."
              }
            </p>
            <div className="mt-8">
              <Button
                onClick={handleViewFullAnalysis}
                variant="outline"
                className="gap-2 px-6 py-3 rounded-xl"
              >
                <BarChart3 className="h-5 w-5" />
                Check Analysis Status
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <Shield className="h-6 w-6 text-blue-600" />
              <h2 className="text-2xl font-bold text-gray-900">{thesisName}</h2>
            </div>
            {lastScored && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Last scored {new Date(lastScored * 1000).toLocaleDateString()}</span>
              </div>
            )}
          </div>
          <Button
            onClick={handleViewFullAnalysis}
            size="sm"
            className="gap-2"
          >
            View Full Analysis
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Main Thesis Match Score Card */}
        <Card className={cn(
          "p-6 border-0 shadow-lg",
          getScoreBackground(thesisMatchPercent)
        )}>
          <CardContent className="p-0">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-4">
                    <motion.span
                      className="text-5xl font-bold text-gray-900"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8 }}
                    >
                      {animatedScore}%
                    </motion.span>
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        Thesis Match
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Based on {Object.keys(thesisScoring.question_scores || {}).length} criteria
                      </p>
                    </div>
                  </div>

                  <div className="w-80">
                    <Progress
                      value={animatedScore}
                      className="h-3"
                    />
                  </div>
                </div>
              </div>

              {/* Status Badges */}
              <div className="flex flex-col gap-2 items-end">
                <Badge className={cn(
                  "text-sm font-bold px-4 py-2",
                  getMatchStatusBadge(thesisMatchPercent).color
                )}>
                  {getMatchStatusBadge(thesisMatchPercent).text}
                </Badge>

                {(bonusTotal > 0 || penaltyTotal > 0) && (
                  <Badge variant="outline" className="text-xs">
                    {bonusTotal > 0 && `+${bonusTotal} bonus`}
                    {bonusTotal > 0 && penaltyTotal > 0 && ", "}
                    {penaltyTotal > 0 && `-${penaltyTotal} penalty`}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Score Summary Section */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="border-0 shadow-sm bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                <BarChart3 className="h-5 w-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Core Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {coreScore.toFixed(1)} / {maxPossibleScore}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {bonusTotal > 0 && (
          <Card className="border-0 shadow-sm bg-green-50/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-100 text-green-600">
                  <TrendingUp className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-600">Bonus Points</p>
                  <p className="text-2xl font-bold text-green-900">+{bonusTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {penaltyTotal > 0 && (
          <Card className="border-0 shadow-sm bg-red-50/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-red-100 text-red-600">
                  <TrendingDown className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-red-600">Penalty Points</p>
                  <p className="text-2xl font-bold text-red-900">-{penaltyTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Placeholder for upcoming AI Summary */}
      <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
            <Target className="h-5 w-5 text-purple-600" />
            AI Analysis Summary
          </CardTitle>
          <p className="text-sm text-gray-600">
            Coming soon: AI-powered highlights and insights about this deal's performance
          </p>
        </CardHeader>
      </Card>
    </div>
  )
}
