"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>ircle, Al<PERSON>Triangle, Copy, Download, <PERSON>, <PERSON>rkles } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { FounderSignals } from "@/lib/api/founder-api"

interface FounderInsightsProps {
  signals: FounderSignals | null
  founderName: string
  className?: string
}

export function FounderInsights({ signals, founderName, className }: FounderInsightsProps) {
  const { toast } = useToast()

  const handleCopyInsights = async () => {
    if (!signals) return
    
    const insightsText = `
${founderName} - Founder Analysis

Score: ${signals.score}/100

Strengths:
${signals.strengths.items.map(item => `• ${item}`).join('\n')}

Risks:
${signals.risks.items.map(item => `• ${item}`).join('\n')}

Tags: ${signals.tags.join(', ')}

Skill Profile:
• Tech: ${signals.skillProfile.tech}/10
• Product: ${signals.skillProfile.product}/10
• Business: ${signals.skillProfile.business}/10
• Operations: ${signals.skillProfile.operations}/10
• Fundraising: ${signals.skillProfile.fundraising}/10
    `.trim()

    try {
      await navigator.clipboard.writeText(insightsText)
      toast({
        title: "Copied to clipboard",
        description: "Founder insights have been copied to your clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDownloadPDF = () => {
    // Placeholder for PDF generation
    toast({
      title: "PDF Download",
      description: "PDF generation will be available soon.",
    })
  }

  if (!signals) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No AI insights available</p>
        </CardContent>
      </Card>
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default'
    if (score >= 60) return 'secondary'
    return 'destructive'
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Insights
            <Badge variant="outline" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              AI Generated
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyInsights}
              className="h-7 px-2"
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownloadPDF}
              className="h-7 px-2"
            >
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-4">
        {/* Score */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
          <span className="text-sm font-medium">Overall Score</span>
          <Badge variant={getScoreBadgeVariant(signals.score)} className="text-sm">
            {signals.score}/100
          </Badge>
        </div>

        {/* Tags */}
        {signals.tags.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Profile Tags</h4>
            <div className="flex flex-wrap gap-1">
              {signals.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Strengths */}
        {signals.strengths.items.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Strengths
            </h4>
            <div className="space-y-2">
              {signals.strengths.items.map((strength, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-muted-foreground">{strength}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Risks */}
        {signals.risks.items.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              Risks & Areas for Growth
            </h4>
            <div className="space-y-2">
              {signals.risks.items.map((risk, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-3 w-3 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span className="text-muted-foreground">{risk}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Attribution */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground flex items-center gap-1">
            <Brain className="h-3 w-3" />
            Generated by AI from founder history and experience data
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export default FounderInsights
