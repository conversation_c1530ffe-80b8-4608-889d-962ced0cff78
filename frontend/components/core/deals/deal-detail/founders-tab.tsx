"use client"

import { motion } from "framer-motion"
import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Linkedin, 
  Twitter, 
  Github, 
  ExternalLink,
  Star,
  Building,
  Award,
  Edit2,
  Check,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { DealAPI } from "@/lib/api/deal-api"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

const getScoreColor = (score?: number) => {
  if (!score) return 'text-muted-foreground'
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBackground = (score?: number) => {
  if (!score) return 'bg-gray-50'
  if (score >= 80) return 'bg-green-50'
  if (score >= 60) return 'bg-yellow-50'
  return 'bg-red-50'
}

// LinkedIn URL validation
const isValidLinkedInUrl = (url: string): boolean => {
  if (!url) return false
  const linkedinPattern = /^https?:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/
  return linkedinPattern.test(url)
}

export function FoundersTab({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()
  const [editingFounderIndex, setEditingFounderIndex] = useState<number | null>(null)
  const [editingLinkedIn, setEditingLinkedIn] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)

  // Keep local founders in sync with deal prop if it changes (e.g. after parent update)
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  if (founders.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="users" />
        <EmptyPlaceholder.Title>No founder data available for this deal</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Founder profiles will appear here once they're added to the deal.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  // Helper function to display field or "Not Found"
  const displayField = (value: any): string => {
    if (value === null || value === undefined || value === '') {
      return 'Not Found'
    }
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(', ') : 'Not Found'
    }
    return String(value).toWellFormed()
  }

  // Helper function to display experience array
  const displayExperience = (experience: any): string => {
    if (!Array.isArray(experience) || experience.length === 0) {
      return 'Not Found'
    }
    
    return experience.map(exp => {
      const company = exp.company || 'Unknown Company'
      const position = exp.position || 'Unknown Position'
      return `${position} at ${company}`
    }).join(', ')
  }

  // Helper function to display education array
  const displayEducation = (education: any): string => {
    if (!Array.isArray(education) || education.length === 0) {
      return 'Not Found'
    }
    
    return education.map(edu => {
      const institution = edu.institution || 'Unknown Institution'
      const degree = edu.degree || 'Unknown Degree'
      const field = edu.field || 'Unknown Field'
      return `${degree} in ${field} from ${institution}`
    }).join(', ')
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  const handleEditLinkedIn = (founderIndex: number, currentLinkedIn: string) => {
    setEditingFounderIndex(founderIndex)
    setEditingLinkedIn(currentLinkedIn || '')
  }

  const handleCancelEdit = () => {
    setEditingFounderIndex(null)
    setEditingLinkedIn('')
  }

  const handleSaveLinkedIn = async () => {
    if (editingFounderIndex === null) return

    // Validate LinkedIn URL
    if (editingLinkedIn && !isValidLinkedInUrl(editingLinkedIn)) {
      toast({
        title: "Invalid LinkedIn URL",
        description: "Please enter a valid LinkedIn profile URL (e.g., https://linkedin.com/in/username)",
        variant: "destructive",
      })
      return
    }

    setIsUpdating(true)
    try {
      // Create updated founders array
      const updatedFounders = [...founders]
      updatedFounders[editingFounderIndex] = {
        ...updatedFounders[editingFounderIndex],
        linkedin: editingLinkedIn || undefined
      }

      // Update deal via API
      const updatedDeal = await DealAPI.updateDeal(deal.id, {
        founders: updatedFounders
      })

      // Update local state
      setFounders(updatedFounders)
      if (onDealUpdate) {
        onDealUpdate(updatedDeal as DealDetailData)
      }

      setEditingFounderIndex(null)
      setEditingLinkedIn('')

      toast({
        title: "LinkedIn Updated",
        description: "Founder's LinkedIn profile has been updated successfully.",
      })
    } catch (error) {
      console.error('Failed to update LinkedIn:', error)
      toast({
        title: "Error",
        description: "Failed to update LinkedIn profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Founders Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {founders.map((founder, index) => (
          <motion.div
            key={founder._id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={founder.profile_picture ?? undefined} alt={founder.name || 'Founder'} />
                      <AvatarFallback className="text-lg">
                        {founder.name ? founder.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'F'}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div>
                          <h3 className="font-semibold text-lg">{displayField(founder.name)}</h3>
                          {(() => {
                            const roleOrTitle = founder.role;
                            if (Array.isArray(roleOrTitle)) {
                              return roleOrTitle.filter(Boolean)
                                .map((r, idx) => (
                                  <span key={idx}>
                                    {String(r).toUpperCase()}
                                    {idx < roleOrTitle.length - 1 ? ', ' : ''}
                                  </span>
                                ));
                            } else if (roleOrTitle) {
                              return String(roleOrTitle).toUpperCase();
                            }
                            return null;
                          })()}
                          <p className="text-sm text-muted-foreground">{}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Founder Details */}
                  <div className="space-y-3">
                    {/* LinkedIn - Editable */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">LinkedIn:</span>
                      {editingFounderIndex === index ? (
                        <div className="flex items-center gap-2">
                          <Input
                            type="url"
                            value={editingLinkedIn}
                            onChange={(e) => setEditingLinkedIn(e.target.value)}
                            placeholder="https://linkedin.com/in/username"
                            className="w-64 text-xs"
                            disabled={isUpdating}
                          />
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleSaveLinkedIn}
                            disabled={isUpdating}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={handleCancelEdit}
                            disabled={isUpdating}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">
                            {displayField(founder.linkedin)}
                          </span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditLinkedIn(index, founder.linkedin || '')}
                            className="h-6 w-6 p-0"
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Email */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Email:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.email)}</span>
                    </div>

                    {/* Serial Founder */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Serial Founder:</span>
                      <span className="text-sm text-muted-foreground">
                        {founder.serial_founder !== undefined ? (founder.serial_founder ? 'Yes' : 'No') : 'Not Found'}
                      </span>
                    </div>

                    {/* Experience */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Experience:</span>
                      <span className="text-sm text-muted-foreground">{displayExperience(founder.experience)}</span>
                    </div>

                    {/* Skills */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Skills:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.skills)}</span>
                    </div>

                    {/* Education */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Education:</span>
                      <span className="text-sm text-muted-foreground">{displayEducation(founder.education)}</span>
                    </div>

                    {/* Achievements */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Achievements:</span>
                      <span className="text-sm text-muted-foreground">{displayField(founder.achievements)}</span>
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="flex items-center gap-2 pt-2 border-t">
                    {founder.linkedin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSocialClick(founder.linkedin!)}
                        className="gap-2"
                      >
                        <Linkedin className="h-4 w-4" />
                        LinkedIn
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Team Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardContent className="p-6">
            <h3 className="font-semibold mb-4">Team Summary</h3>
            
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 rounded-lg bg-muted/50">
                <div className="text-2xl font-bold">{founders.length}</div>
                <div className="text-sm text-muted-foreground">
                  Founder{founders.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
