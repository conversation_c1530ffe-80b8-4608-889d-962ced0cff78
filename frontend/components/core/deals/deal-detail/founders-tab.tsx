"use client"

import { motion } from "framer-motion"
import { use<PERSON>tate, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Linkedin,
  Twitter,
  Github,
  ExternalLink,
  Star,
  Building,
  Award,
  Edit2,
  Check,
  X,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Brain,
  Download,
  Copy,
  Sparkles,
  HelpCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { DealAPI } from "@/lib/api/deal-api"
import { FounderAPI, EnrichedFounder, FounderAnalysisResponse } from "@/lib/api/founder-api"
import { useToast } from "@/components/ui/use-toast"
import { FounderRadarChart } from "./founder-radar-chart"
import { FounderTimeline } from "./founder-timeline"
import { FounderInsights } from "./founder-insights"
import { FounderSkills } from "./founder-skills"
import { FounderEducation } from "./founder-education"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

// Enhanced founder analysis state
interface FounderAnalysisState {
  enrichedFounders: EnrichedFounder[]
  isLoading: boolean
  error: string | null
  teamSummary: string | null
  isGeneratingSummary: boolean
}

const getScoreColor = (score?: number) => {
  if (!score) return 'text-muted-foreground'
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBackground = (score?: number) => {
  if (!score) return 'bg-gray-50'
  if (score >= 80) return 'bg-green-50'
  if (score >= 60) return 'bg-yellow-50'
  return 'bg-red-50'
}

// LinkedIn URL validation
const isValidLinkedInUrl = (url: string): boolean => {
  if (!url) return false
  const linkedinPattern = /^https?:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/
  return linkedinPattern.test(url)
}

export function FoundersTab({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()
  const [editingFounderIndex, setEditingFounderIndex] = useState<number | null>(null)
  const [editingLinkedIn, setEditingLinkedIn] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)

  // Enhanced founder analysis state
  const [analysisState, setAnalysisState] = useState<FounderAnalysisState>({
    enrichedFounders: [],
    isLoading: false,
    error: null,
    teamSummary: null,
    isGeneratingSummary: false
  })

  // Keep local founders in sync with deal prop if it changes (e.g. after parent update)
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  // Fetch enriched founder data
  useEffect(() => {
    const fetchEnrichedFounders = async () => {
      if (!deal.id) return

      setAnalysisState(prev => ({ ...prev, isLoading: true, error: null }))

      try {
        const response = await FounderAPI.getDealFounders(deal.id)
        setAnalysisState(prev => ({
          ...prev,
          enrichedFounders: response.founders,
          isLoading: false
        }))
      } catch (error) {
        console.error('Failed to fetch enriched founders:', error)
        setAnalysisState(prev => ({
          ...prev,
          error: 'Failed to load enriched founder data',
          isLoading: false
        }))
      }
    }

    fetchEnrichedFounders()
  }, [deal.id])

  // Generate team summary
  const generateTeamSummary = async () => {
    if (!deal.id) return

    setAnalysisState(prev => ({ ...prev, isGeneratingSummary: true }))

    try {
      const response = await FounderAPI.generateFounderSummary(deal.id)
      setAnalysisState(prev => ({
        ...prev,
        teamSummary: response.summary,
        isGeneratingSummary: false
      }))

      toast({
        title: "Team Summary Generated",
        description: "AI-powered team analysis has been generated successfully.",
      })
    } catch (error) {
      console.error('Failed to generate team summary:', error)
      setAnalysisState(prev => ({ ...prev, isGeneratingSummary: false }))

      toast({
        title: "Error",
        description: "Failed to generate team summary. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Calculate team metrics
  const teamMetrics = {
    totalFounders: analysisState.enrichedFounders.length || founders.length,
    avgScore: analysisState.enrichedFounders.length > 0
      ? Math.round(analysisState.enrichedFounders.reduce((sum, f) => sum + (f.signals?.score || 0), 0) / analysisState.enrichedFounders.length)
      : 0,
    topTags: analysisState.enrichedFounders.length > 0
      ? [...new Set(analysisState.enrichedFounders.flatMap(f => f.signals?.tags || []))].slice(0, 3)
      : []
  }

  if (founders.length === 0 && analysisState.enrichedFounders.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="users" />
        <EmptyPlaceholder.Title>No founder data available for this deal</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Founder profiles will appear here once they're added to the deal.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  // Helper function to display field or "Not Found"
  const displayField = (value: any): string => {
    if (value === null || value === undefined || value === '') {
      return 'Not Found'
    }
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(', ') : 'Not Found'
    }
    return String(value).toWellFormed()
  }

  // Helper function to display experience array
  const displayExperience = (experience: any): string => {
    if (!Array.isArray(experience) || experience.length === 0) {
      return 'Not Found'
    }
    
    return experience.map(exp => {
      const company = exp.company || 'Unknown Company'
      const position = exp.position || 'Unknown Position'
      return `${position} at ${company}`
    }).join(', ')
  }

  // Helper function to display education array
  const displayEducation = (education: any): string => {
    if (!Array.isArray(education) || education.length === 0) {
      return 'Not Found'
    }
    
    return education.map(edu => {
      const institution = edu.institution || 'Unknown Institution'
      const degree = edu.degree || 'Unknown Degree'
      const field = edu.field || 'Unknown Field'
      return `${degree} in ${field} from ${institution}`
    }).join(', ')
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  const handleEditLinkedIn = (founderIndex: number, currentLinkedIn: string) => {
    setEditingFounderIndex(founderIndex)
    setEditingLinkedIn(currentLinkedIn || '')
  }

  const handleCancelEdit = () => {
    setEditingFounderIndex(null)
    setEditingLinkedIn('')
  }

  const handleSaveLinkedIn = async () => {
    if (editingFounderIndex === null) return

    // Validate LinkedIn URL
    if (editingLinkedIn && !isValidLinkedInUrl(editingLinkedIn)) {
      toast({
        title: "Invalid LinkedIn URL",
        description: "Please enter a valid LinkedIn profile URL (e.g., https://linkedin.com/in/username)",
        variant: "destructive",
      })
      return
    }

    setIsUpdating(true)
    try {
      // Create updated founders array
      const updatedFounders = [...founders]
      updatedFounders[editingFounderIndex] = {
        ...updatedFounders[editingFounderIndex],
        linkedin: editingLinkedIn || undefined
      }

      // Update deal via API
      const updatedDeal = await DealAPI.updateDeal(deal.id, {
        founders: updatedFounders
      })

      // Update local state
      setFounders(updatedFounders)
      if (onDealUpdate) {
        onDealUpdate(updatedDeal as DealDetailData)
      }

      setEditingFounderIndex(null)
      setEditingLinkedIn('')

      toast({
        title: "LinkedIn Updated",
        description: "Founder's LinkedIn profile has been updated successfully.",
      })
    } catch (error) {
      console.error('Failed to update LinkedIn:', error)
      toast({
        title: "Error",
        description: "Failed to update LinkedIn profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Team Overview Header - Sticky */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b"
        >
          <Card className="border-0 shadow-sm bg-background/60 backdrop-blur-md">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">{teamMetrics.totalFounders}</span>
                    <span className="text-sm text-muted-foreground">
                      Founder{teamMetrics.totalFounders !== 1 ? 's' : ''}
                    </span>
                  </div>

                  {teamMetrics.avgScore > 0 && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-muted-foreground" />
                      <span className="text-2xl font-bold">{teamMetrics.avgScore}</span>
                      <span className="text-sm text-muted-foreground">/100</span>
                      <Badge variant="outline" className="text-xs">Avg Score</Badge>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {teamMetrics.topTags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {teamMetrics.topTags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}

                  <Button
                    onClick={generateTeamSummary}
                    disabled={analysisState.isGeneratingSummary}
                    className="gap-2"
                    size="sm"
                  >
                    <Brain className="h-4 w-4" />
                    {analysisState.isGeneratingSummary ? 'Generating...' : 'Generate Summary'}
                  </Button>
                </div>
              </div>

              {analysisState.teamSummary && (
                <div className="mt-4 p-4 rounded-lg bg-muted/50 border">
                  <p className="text-sm text-muted-foreground">{analysisState.teamSummary}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Founder Profile Cards */}
        <div className="space-y-8">
          {analysisState.isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Loading enriched founder data...</p>
            </div>
          ) : analysisState.error ? (
            <div className="text-center py-8">
              <p className="text-sm text-destructive mb-4">{analysisState.error}</p>
              <p className="text-xs text-muted-foreground">Showing basic founder information instead</p>
            </div>
          ) : null}

          {/* Show enriched founders if available, otherwise show basic founders */}
          {(analysisState.enrichedFounders.length > 0 ? analysisState.enrichedFounders : founders.map(f => ({ founder: f }))).map((enrichedFounder, index) => {
            const founder = 'founder' in enrichedFounder ? enrichedFounder.founder : enrichedFounder
            const isEnriched = 'experiences' in enrichedFounder

            return (
              <motion.div
                key={founder.id || founder._id || index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="space-y-6"
              >
                {/* Founder Identity Card */}
                <Card className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-20 w-20">
                        <AvatarImage
                          src={founder.profilePicture || founder.profile_picture}
                          alt={founder.fullName || founder.name || 'Founder'}
                        />
                        <AvatarFallback className="text-lg">
                          {(founder.fullName || founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div>
                            <h3 className="font-bold text-xl">
                              {founder.fullName || founder.name || 'Unknown Founder'}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {founder.title || (Array.isArray(founder.role) ? founder.role.join(', ') : founder.role) || 'Role not specified'}
                            </p>
                            {founder.country && (
                              <div className="flex items-center gap-1 mt-1">
                                <MapPin className="h-3 w-3 text-muted-foreground" />
                                <span className="text-xs text-muted-foreground">{founder.country}</span>
                              </div>
                            )}
                          </div>

                          {isEnriched && enrichedFounder.signals && (
                            <Badge
                              variant={enrichedFounder.signals.score >= 80 ? 'default' : enrichedFounder.signals.score >= 60 ? 'secondary' : 'destructive'}
                              className="text-sm"
                            >
                              {enrichedFounder.signals.score}/100
                            </Badge>
                          )}
                        </div>

                        {/* Social Links */}
                        <div className="flex items-center gap-2 mt-3">
                          {(founder.linkedinUrl || founder.linkedin) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSocialClick(founder.linkedinUrl || founder.linkedin!)}
                              className="gap-2 h-8"
                            >
                              <Linkedin className="h-4 w-4" />
                              LinkedIn
                            </Button>
                          )}
                          {(founder.twitterUrl || founder.twitter) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSocialClick(founder.twitterUrl || founder.twitter!)}
                              className="gap-2 h-8"
                            >
                              <Twitter className="h-4 w-4" />
                              Twitter
                            </Button>
                          )}
                          {(founder.githubUrl || founder.github) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSocialClick(founder.githubUrl || founder.github!)}
                              className="gap-2 h-8"
                            >
                              <Github className="h-4 w-4" />
                              GitHub
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Enhanced Analysis Grid - Only show for enriched founders */}
                {isEnriched && (
                  <div className="grid gap-6 lg:grid-cols-2">
                    {/* Skill Profile Radar Chart */}
                    {enrichedFounder.signals?.skillProfile && (
                      <FounderRadarChart
                        skillProfile={enrichedFounder.signals.skillProfile}
                        founderName={founder.fullName || founder.name || 'Founder'}
                      />
                    )}

                    {/* AI Insights */}
                    <FounderInsights
                      signals={enrichedFounder.signals}
                      founderName={founder.fullName || founder.name || 'Founder'}
                    />

                    {/* Experience Timeline */}
                    {enrichedFounder.experiences && enrichedFounder.experiences.length > 0 && (
                      <FounderTimeline experiences={enrichedFounder.experiences} />
                    )}

                    {/* Education */}
                    {enrichedFounder.education && enrichedFounder.education.length > 0 && (
                      <FounderEducation education={enrichedFounder.education} />
                    )}

                    {/* Skills Tag Cloud */}
                    {enrichedFounder.skills && enrichedFounder.skills.length > 0 && (
                      <FounderSkills skills={enrichedFounder.skills} className="lg:col-span-2" />
                    )}
                  </div>
                )}

                {/* Fallback for basic founder data */}
                {!isEnriched && (
                  <Card>
                    <CardContent className="p-6">
                      <div className="space-y-3">
                        {/* LinkedIn - Editable */}
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">LinkedIn:</span>
                          {editingFounderIndex === index ? (
                            <div className="flex items-center gap-2">
                              <Input
                                type="url"
                                value={editingLinkedIn}
                                onChange={(e) => setEditingLinkedIn(e.target.value)}
                                placeholder="https://linkedin.com/in/username"
                                className="w-64 text-xs"
                                disabled={isUpdating}
                              />
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={handleSaveLinkedIn}
                                disabled={isUpdating}
                                className="h-8 w-8 p-0"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={handleCancelEdit}
                                disabled={isUpdating}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">
                                {displayField(founder.linkedin)}
                              </span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleEditLinkedIn(index, founder.linkedin || '')}
                                className="h-6 w-6 p-0"
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>

                        {/* Basic founder fields */}
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Email:</span>
                          <span className="text-sm text-muted-foreground">{displayField(founder.email)}</span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Serial Founder:</span>
                          <span className="text-sm text-muted-foreground">
                            {founder.serial_founder !== undefined ? (founder.serial_founder ? 'Yes' : 'No') : 'Not Found'}
                          </span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Experience:</span>
                          <span className="text-sm text-muted-foreground">{displayExperience(founder.experience)}</span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Skills:</span>
                          <span className="text-sm text-muted-foreground">{displayField(founder.skills)}</span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Education:</span>
                          <span className="text-sm text-muted-foreground">{displayEducation(founder.education)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Separator between founders */}
                {index < (analysisState.enrichedFounders.length > 0 ? analysisState.enrichedFounders : founders).length - 1 && (
                  <Separator className="my-8" />
                )}
              </motion.div>
            )
          })}
        </div>
      </div>
    </TooltipProvider>
  )
}
