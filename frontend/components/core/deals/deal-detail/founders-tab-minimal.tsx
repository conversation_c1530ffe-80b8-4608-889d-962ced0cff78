"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, Brain, Linkedin } from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Founder } from "@/lib/types/deal"
import { useToast } from "@/components/ui/use-toast"

interface FoundersTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

export function FoundersTabMinimal({ deal, onDealUpdate }: FoundersTabProps) {
  const [founders, setFounders] = useState<Founder[]>(deal.founders || [])
  const { toast } = useToast()

  // Keep local founders in sync with deal prop if it changes
  useEffect(() => {
    setFounders(deal.founders || [])
  }, [deal.founders])

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  if (founders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="flex h-24 w-24 items-center justify-center rounded-full bg-muted mb-4">
          <Users className="h-12 w-12" />
        </div>
        <h2 className="text-2xl font-bold mb-2">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Team Overview Header */}
      <Card className="border-0 shadow-sm bg-background/60 backdrop-blur-md">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-muted-foreground" />
                <span className="text-2xl font-bold">{founders.length}</span>
                <span className="text-sm text-muted-foreground">
                  Founder{founders.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                onClick={() => toast({ title: "AI Summary", description: "Feature coming soon!" })}
                className="gap-2"
                size="sm"
              >
                <Brain className="h-4 w-4" />
                Generate Summary
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Founder Cards */}
      <div className="space-y-6">
        {founders.map((founder, index) => (
          <Card key={founder._id || index} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage 
                    src={founder.profile_picture} 
                    alt={founder.name || 'Founder'} 
                  />
                  <AvatarFallback className="text-lg">
                    {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-xl">
                    {founder.name || 'Unknown Founder'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                  </p>
                  
                  {/* Social Links */}
                  <div className="flex items-center gap-2 mt-3">
                    {founder.linkedin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSocialClick(founder.linkedin!)}
                        className="gap-2 h-8"
                      >
                        <Linkedin className="h-4 w-4" />
                        LinkedIn
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
