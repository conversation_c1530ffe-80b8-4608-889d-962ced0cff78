"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lt<PERSON>, TooltipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { HelpCircle } from "lucide-react"

interface SkillProfile {
  tech: number
  product: number
  business: number
  operations: number
  fundraising: number
}

interface FounderRadarChartProps {
  skillProfile: SkillProfile
  founderName: string
  className?: string
}

const skillDefinitions = {
  tech: "Technical expertise including programming, system architecture, and engineering leadership",
  product: "Product management, user experience design, and product strategy capabilities",
  business: "Business development, strategy, market analysis, and commercial acumen",
  operations: "Operational efficiency, process management, and organizational leadership",
  fundraising: "Capital raising experience, investor relations, and financial planning"
}

export function FounderRadarChart({ skillProfile, founderName, className }: FounderRadarChartProps) {
  // Transform skill profile data for radar chart
  const chartData = [
    {
      skill: 'Tech',
      value: skillProfile.tech,
      fullMark: 10
    },
    {
      skill: 'Product',
      value: skillProfile.product,
      fullMark: 10
    },
    {
      skill: 'Business',
      value: skillProfile.business,
      fullMark: 10
    },
    {
      skill: 'Operations',
      value: skillProfile.operations,
      fullMark: 10
    },
    {
      skill: 'Fundraising',
      value: skillProfile.fundraising,
      fullMark: 10
    }
  ]

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          Skill Profile
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="h-3 w-3 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p className="text-xs">AI-generated skill assessment based on founder's experience and background</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="h-48 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={chartData} margin={{ top: 10, right: 10, bottom: 10, left: 10 }}>
              <PolarGrid 
                stroke="#e2e8f0" 
                strokeWidth={1}
                radialLines={true}
              />
              <PolarAngleAxis 
                dataKey="skill" 
                tick={{ fontSize: 11, fill: '#64748b' }}
                className="text-xs"
              />
              <PolarRadiusAxis 
                angle={90} 
                domain={[0, 10]} 
                tick={{ fontSize: 9, fill: '#94a3b8' }}
                tickCount={6}
              />
              <Radar
                name={founderName}
                dataKey="value"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.1}
                strokeWidth={2}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
              />
            </RadarChart>
          </ResponsiveContainer>
        </div>
        
        {/* Skill Legend with Tooltips */}
        <div className="mt-4 space-y-2">
          {Object.entries(skillDefinitions).map(([key, definition]) => {
            const skillKey = key as keyof SkillProfile
            const value = skillProfile[skillKey]
            return (
              <TooltipProvider key={key}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center justify-between text-xs cursor-help">
                      <span className="capitalize text-muted-foreground">{key}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-500 transition-all duration-500"
                            style={{ width: `${(value / 10) * 100}%` }}
                          />
                        </div>
                        <span className="font-medium w-6 text-right">{value}</span>
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p className="text-xs">{definition}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

export default FounderRadarChart
