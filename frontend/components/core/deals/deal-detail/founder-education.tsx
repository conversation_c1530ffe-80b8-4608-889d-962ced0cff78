"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { GraduationCap, MapPin, Calendar } from "lucide-react"
import { FounderEducation } from "@/lib/api/founder-api"

interface FounderEducationProps {
  education: FounderEducation[]
  className?: string
}

// Top universities for badge highlighting
const topUniversities = [
  'harvard', 'stanford', 'mit', 'cambridge', 'oxford', 'yale', 'princeton', 'columbia',
  'university of pennsylvania', 'dartmouth', 'brown', 'cornell', 'duke', 'northwestern',
  'university of chicago', 'johns hopkins', 'rice', 'vanderbilt', 'notre dame',
  'carnegie mellon', 'emory', 'georgetown', 'university of california berkeley',
  'university of california los angeles', 'university of michigan', 'university of virginia',
  'wake forest', 'tufts', 'boston college', 'new york university', 'brand<PERSON>s',
  'university of rochester', 'case western reserve', 'tulane', 'boston university',
  'northeastern', 'rensselaer polytechnic', 'university of miami', 'georgia tech',
  'university of illinois', 'university of wisconsin', 'university of texas',
  'university of washington', 'university of southern california', 'pepperdine',
  'santa clara', 'loyola marymount', 'university of san diego', 'chapman',
  'london school of economics', 'imperial college', 'university college london',
  'kings college london', 'warwick', 'edinburgh', 'manchester', 'bristol',
  'glasgow', 'sheffield', 'birmingham', 'leeds', 'liverpool', 'southampton',
  'insead', 'london business school', 'wharton', 'kellogg', 'booth', 'sloan',
  'haas', 'stern', 'fuqua', 'darden', 'kenan-flagler', 'mccombs', 'anderson',
  'marshall', 'foster', 'carlson', 'kelley', 'fisher', 'smeal', 'broad'
]

function isTopUniversity(schoolName: string): boolean {
  const schoolLower = schoolName.toLowerCase()
  return topUniversities.some(uni => schoolLower.includes(uni))
}

export function FounderEducation({ education, className }: FounderEducationProps) {
  if (education.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Education</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No education data available</p>
        </CardContent>
      </Card>
    )
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return null
    try {
      const date = new Date(dateString)
      return date.getFullYear().toString()
    } catch {
      return dateString
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <GraduationCap className="h-4 w-4" />
          Education
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-4">
        {education.map((edu, index) => (
          <div key={edu.id} className="space-y-2">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm leading-tight">
                  {edu.schoolName}
                  {isTopUniversity(edu.schoolName) && (
                    <Badge variant="default" className="ml-2 text-xs">
                      Top University
                    </Badge>
                  )}
                </h4>
                
                {edu.degrees.length > 0 && (
                  <div className="mt-1 space-y-1">
                    {edu.degrees.map((degree, degreeIndex) => (
                      <div key={degreeIndex} className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {degree}
                        </span>
                        {edu.majors[degreeIndex] && (
                          <Badge variant="outline" className="text-xs">
                            {edu.majors[degreeIndex]}
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                )}
                
                {edu.majors.length > 0 && edu.degrees.length === 0 && (
                  <div className="mt-1">
                    <div className="flex flex-wrap gap-1">
                      {edu.majors.map((major, majorIndex) => (
                        <Badge key={majorIndex} variant="outline" className="text-xs">
                          {major}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {(edu.startDate || edu.endDate) && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {edu.startDate && formatDate(edu.startDate)}
                    {edu.startDate && edu.endDate && ' - '}
                    {edu.endDate && formatDate(edu.endDate)}
                  </span>
                </div>
              )}
            </div>
            
            {edu.location && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>{edu.location}</span>
              </div>
            )}
            
            {index < education.length - 1 && (
              <div className="border-b border-border/50 pt-2" />
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export default FounderEducation
