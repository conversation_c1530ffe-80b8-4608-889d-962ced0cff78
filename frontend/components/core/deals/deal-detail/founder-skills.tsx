"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp, Code, Briefcase, Users, Wrench } from "lucide-react"
import { FounderSkill } from "@/lib/api/founder-api"

interface FounderSkillsProps {
  skills: FounderSkill[]
  className?: string
}

// Skill categorization
const skillCategories = {
  technical: {
    icon: Code,
    label: "Technical",
    keywords: ['python', 'javascript', 'react', 'node', 'aws', 'docker', 'kubernetes', 'sql', 'mongodb', 'api', 'backend', 'frontend', 'fullstack', 'devops', 'cloud', 'machine learning', 'ai', 'data science', 'analytics', 'programming', 'software', 'development', 'engineering', 'architecture', 'database', 'web', 'mobile', 'ios', 'android', 'java', 'c++', 'golang', 'rust', 'scala', 'php', 'ruby', 'swift', 'kotlin', 'typescript', 'html', 'css', 'git', 'linux', 'unix', 'bash', 'shell', 'microservices', 'serverless', 'blockchain', 'cryptocurrency', 'cybersecurity', 'security', 'testing', 'qa', 'automation', 'ci/cd', 'jenkins', 'terraform', 'ansible']
  },
  business: {
    icon: Briefcase,
    label: "Business",
    keywords: ['sales', 'marketing', 'strategy', 'business development', 'partnerships', 'negotiation', 'fundraising', 'investor relations', 'finance', 'accounting', 'budgeting', 'financial planning', 'revenue', 'growth', 'scaling', 'operations', 'consulting', 'analysis', 'market research', 'competitive analysis', 'pricing', 'go-to-market', 'customer acquisition', 'retention', 'crm', 'salesforce', 'hubspot', 'digital marketing', 'seo', 'sem', 'social media', 'content marketing', 'email marketing', 'advertising', 'ppc', 'analytics', 'metrics', 'kpis', 'roi', 'conversion', 'funnel', 'lead generation', 'b2b', 'b2c', 'saas', 'enterprise', 'startup', 'entrepreneurship', 'venture capital', 'private equity', 'ipo', 'mergers', 'acquisitions', 'due diligence', 'valuation', 'financial modeling', 'excel', 'powerpoint', 'presentation']
  },
  leadership: {
    icon: Users,
    label: "Leadership & Soft Skills",
    keywords: ['leadership', 'management', 'team building', 'mentoring', 'coaching', 'communication', 'presentation', 'public speaking', 'negotiation', 'conflict resolution', 'decision making', 'strategic thinking', 'problem solving', 'critical thinking', 'creativity', 'innovation', 'adaptability', 'resilience', 'emotional intelligence', 'empathy', 'collaboration', 'teamwork', 'cross-functional', 'stakeholder management', 'change management', 'project management', 'agile', 'scrum', 'kanban', 'lean', 'six sigma', 'process improvement', 'organizational development', 'culture', 'diversity', 'inclusion', 'hiring', 'recruiting', 'talent acquisition', 'performance management', 'employee engagement', 'retention', 'training', 'development', 'succession planning']
  },
  product: {
    icon: Wrench,
    label: "Product & Design",
    keywords: ['product management', 'product strategy', 'roadmap', 'requirements', 'user experience', 'ux', 'user interface', 'ui', 'design', 'wireframing', 'prototyping', 'user research', 'usability testing', 'a/b testing', 'product analytics', 'user stories', 'acceptance criteria', 'backlog', 'prioritization', 'feature development', 'mvp', 'product launch', 'go-to-market', 'customer feedback', 'user interviews', 'surveys', 'personas', 'journey mapping', 'information architecture', 'interaction design', 'visual design', 'branding', 'graphic design', 'adobe', 'photoshop', 'illustrator', 'sketch', 'figma', 'invision', 'principle', 'framer', 'html', 'css', 'responsive design', 'mobile design', 'web design', 'accessibility', 'design systems', 'style guides', 'design thinking', 'human-centered design', 'service design', 'design research']
  }
}

function categorizeSkill(skill: string): keyof typeof skillCategories {
  const skillLower = skill.toLowerCase()
  
  for (const [category, config] of Object.entries(skillCategories)) {
    if (config.keywords.some(keyword => skillLower.includes(keyword))) {
      return category as keyof typeof skillCategories
    }
  }
  
  // Default to business if no match
  return 'business'
}

export function FounderSkills({ skills, className }: FounderSkillsProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['technical', 'business']))
  const [showAll, setShowAll] = useState(false)

  if (skills.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Skills</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No skills data available</p>
        </CardContent>
      </Card>
    )
  }

  // Categorize skills
  const categorizedSkills = skills.reduce((acc, skill) => {
    const category = categorizeSkill(skill.skill)
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(skill.skill)
    return acc
  }, {} as Record<keyof typeof skillCategories, string[]>)

  // Sort skills within each category
  Object.keys(categorizedSkills).forEach(category => {
    categorizedSkills[category as keyof typeof skillCategories].sort()
  })

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const displayLimit = showAll ? undefined : 5

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Skills & Expertise</CardTitle>
          <Badge variant="outline" className="text-xs">
            {skills.length} skills
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-4">
        {Object.entries(categorizedSkills).map(([category, categorySkills]) => {
          const config = skillCategories[category as keyof typeof skillCategories]
          const Icon = config.icon
          const isExpanded = expandedCategories.has(category)
          const displaySkills = displayLimit ? categorySkills.slice(0, displayLimit) : categorySkills
          const hasMore = displayLimit && categorySkills.length > displayLimit

          return (
            <div key={category}>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleCategory(category)}
                className="h-auto p-0 mb-2 font-medium text-sm hover:bg-transparent"
              >
                <div className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span>{config.label}</span>
                  <Badge variant="secondary" className="text-xs">
                    {categorySkills.length}
                  </Badge>
                  {isExpanded ? (
                    <ChevronUp className="h-3 w-3" />
                  ) : (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </div>
              </Button>
              
              {isExpanded && (
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-1">
                    {displaySkills.map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                  
                  {hasMore && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAll(!showAll)}
                      className="h-auto p-0 text-xs text-muted-foreground hover:bg-transparent"
                    >
                      {showAll ? 'Show less' : `+${categorySkills.length - displayLimit!} more`}
                    </Button>
                  )}
                </div>
              )}
            </div>
          )
        })}
      </CardContent>
    </Card>
  )
}
