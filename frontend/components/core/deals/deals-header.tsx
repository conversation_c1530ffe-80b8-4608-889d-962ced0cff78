"use client"

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, easeOut } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Search, 
  Plus, 
  Filter, 
  FileText, 
  Zap,
  Settings,
  Calendar,
  SortAsc,
  SortDesc,
  RotateCcw,
  User,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { DateRange } from 'react-day-picker';
import { DealAPI } from '@/lib/api/deal-api';
import { DealDashboardFilters, DealStatus } from '@/lib/types/deal';
import { useToast } from '@/components/ui/use-toast';

interface DealsHeaderProps {
  onSearchChange?: (search: string) => void;
  onFilterChange?: (filters: DealDashboardFilters) => void;
  totalDeals?: number;
  dealCounts?: Record<string, number>;
  loading?: boolean;
}

// Default filter values
const DEFAULT_FILTERS: DealDashboardFilters = {
  status: ['new', 'triage', 'reviewed'],
  assigned_to_me: true,
  sort_by: 'updated_at',
  sort_dir: 'desc'
};

// Sort options
const SORT_OPTIONS = [
  { value: 'created_at', label: 'Created Date', icon: Calendar },
  { value: 'updated_at', label: 'Last Updated', icon: Calendar },
  { value: 'status_updated_at', label: 'Status Updated', icon: Calendar },
];

// Status options with counts
const getStatusOptions = (dealCounts?: Record<string, number>) => [
  { value: 'new', label: 'New', count: dealCounts?.new || 0 },
  { value: 'triage', label: 'Active', count: dealCounts?.triage || 0 },
  { value: 'reviewed', label: 'Reviewed', count: dealCounts?.reviewed || 0 },
  { value: 'approved', label: 'Approved', count: dealCounts?.approved || 0 },
  { value: 'excluded', label: 'Flagged', count: dealCounts?.excluded || 0 },
  { value: 'closed', label: 'Completed', count: dealCounts?.closed || 0 },
];

export function DealsHeader({
  onSearchChange,
  onFilterChange,
  totalDeals = 0,
  dealCounts,
  loading = false
}: DealsHeaderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // State management
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState<DealDashboardFilters>(DEFAULT_FILTERS);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [savingPreferences, setSavingPreferences] = useState(false);
  const [favouritesOnly, setFavouritesOnly] = useState(false);

  // Load filters from URL on mount
  useEffect(() => {
    const urlFilters: DealDashboardFilters = { ...DEFAULT_FILTERS };
    
    // Parse URL parameters
    const status = searchParams?.get('status');
    if (status) {
      urlFilters.status = status.split(',');
    }
    
    const assignedToMe = searchParams?.get('assigned_to_me');
    if (assignedToMe !== null) {
      urlFilters.assigned_to_me = assignedToMe === 'true';
    }
    
    const sortBy = searchParams?.get('sort_by');
    if (sortBy) {
      urlFilters.sort_by = sortBy;
    }
    
    const sortOrder = searchParams?.get('sort_order');
    if (sortOrder) {
      urlFilters.sort_dir = sortOrder;
    }
    
    const createdAtStart = searchParams?.get('created_at_start');
    const createdAtEnd = searchParams?.get('created_at_end');
    if (createdAtStart || createdAtEnd) {
      urlFilters.created_at_start = createdAtStart || undefined;
      urlFilters.created_at_end = createdAtEnd || undefined;
      
      // Set date range for picker
      if (createdAtStart && createdAtEnd) {
        setDateRange({
          from: new Date(createdAtStart),
          to: new Date(createdAtEnd)
        });
      }
    }
    
    const favouritesOnly = searchParams?.get('favourites_only');
    if (favouritesOnly !== null) {
      urlFilters.favourites_only = favouritesOnly === 'true';
      setFavouritesOnly(favouritesOnly === 'true');
    }
    
    setFilters(urlFilters);
  }, [searchParams]);

  // Load user preferences on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const preferences = await DealAPI.getDealPreferences();
        if (preferences.deal_dashboard_filters) {
          // Merge with URL params (URL takes priority)
          const mergedFilters = { ...DEFAULT_FILTERS, ...preferences.deal_dashboard_filters };
          setFilters(mergedFilters);
        }
      } catch (error) {
        console.warn('Failed to load user preferences:', error);
      }
    };
    
    loadPreferences();
  }, []);

  // Update URL when filters change
  const updateURL = useCallback((newFilters: DealDashboardFilters) => {
    const params = new URLSearchParams();
    
    if (newFilters.status && newFilters.status.length > 0) {
      params.set('status', newFilters.status.join(','));
    }
    
    if (newFilters.assigned_to_me !== undefined) {
      params.set('assigned_to_me', newFilters.assigned_to_me.toString());
    }
    
    if (newFilters.sort_by) {
      params.set('sort_by', newFilters.sort_by);
    }
    
    if (newFilters.sort_dir) {
      params.set('sort_order', newFilters.sort_dir);
    }
    
    if (newFilters.created_at_start) {
      params.set('created_at_start', newFilters.created_at_start);
    }
    
    if (newFilters.created_at_end) {
      params.set('created_at_end', newFilters.created_at_end);
    }
    
    if (newFilters.favourites_only !== undefined) {
      params.set('favourites_only', newFilters.favourites_only.toString());
    }
    
    const queryString = params.toString();
    const newURL = queryString ? `/deals?${queryString}` : '/deals';
    router.push(newURL, { scroll: false });
  }, [router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<DealDashboardFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateURL(updatedFilters);
    onFilterChange?.(updatedFilters);
  }, [filters, updateURL, onFilterChange]);

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onSearchChange?.(value);
  };

  // Handle status toggle
  const handleStatusToggle = (status: string) => {
    const currentStatuses = filters.status || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];
    
    handleFilterChange({ status: newStatuses });
  };

  // Handle assigned to me toggle
  const handleAssignedToMeToggle = (checked: boolean) => {
    handleFilterChange({ assigned_to_me: checked });
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    
    if (range?.from && range?.to) {
      const startDate = format(range.from, 'yyyy-MM-dd\'T\'00:00:00\'Z\'');
      const endDate = format(range.to, 'yyyy-MM-dd\'T\'23:59:59\'Z\'');
      handleFilterChange({
        created_at_start: startDate,
        created_at_end: endDate
      });
    } else {
      handleFilterChange({
        created_at_start: undefined,
        created_at_end: undefined
      });
    }
    
    setShowDatePicker(false);
  };

  // Handle sort change
  const handleSortChange = (sortBy: string, sortDir: string) => {
    handleFilterChange({ sort_by: sortBy, sort_dir: sortDir });
  };

  // Reset filters to defaults
  const handleResetFilters = () => {
    setFilters(DEFAULT_FILTERS);
    setDateRange(undefined);
    setSearchValue('');
    updateURL(DEFAULT_FILTERS);
    onFilterChange?.(DEFAULT_FILTERS);
    onSearchChange?.('');
  };

  // Save preferences
  const handleSavePreferences = async () => {
    setSavingPreferences(true);
    try {
      await DealAPI.updateDealPreferences({
        deal_dashboard_filters: filters
      });
      toast({
        title: "Preferences saved",
        description: "Your filter preferences have been saved as default.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSavingPreferences(false);
    }
  };

  // Check if filters differ from defaults
  const hasCustomFilters = JSON.stringify(filters) !== JSON.stringify(DEFAULT_FILTERS);

  // Handle favourites only toggle
  const handleFavouritesOnlyToggle = (checked: boolean) => {
    setFavouritesOnly(checked);
    handleFilterChange({ favourites_only: checked });
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: easeOut
      }
    }
  };

  return (
    <motion.div
      variants={headerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Main Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900">Deal Management</h1>
          <p className="text-gray-500 mt-3 text-xl">Track and manage your investment opportunities</p>
        </div>
        
        {/* Right Side Actions */}
        <div className="flex items-center gap-3">
          <Link href="/forms">
            <Button variant="ghost" size="sm" className="gap-2 text-gray-600 hover:text-gray-900">
              <FileText className="h-4 w-4" />
              Deal Forms
            </Button>
          </Link>
        </div>
      </div>

      {/* Search Bar */}
      <div className="w-full">
        <div className="relative max-w-2xl">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search deals..."
            value={searchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 bg-white border-gray-200 shadow-md w-full h-12 text-base rounded-xl"
          />
        </div>
      </div>

      {/* Filter Controls */}
      <div className="space-y-4">
        {/* Filter Row */}
        <div className="flex flex-wrap items-center gap-3">
          {/* Status Chips */}
          <div className="flex flex-wrap gap-2">
            {getStatusOptions(dealCounts).map((status) => {
              const isActive = filters.status?.includes(status.value);
              return (
                <Button
                  key={status.value}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleStatusToggle(status.value)}
                  className={cn(
                    "rounded-xl px-4 py-2 transition-all duration-200 text-sm font-medium",
                    isActive
                      ? "bg-blue-600 text-white hover:bg-blue-700 shadow-md"
                      : "bg-gray-50 text-gray-700 hover:bg-gray-100 shadow-sm"
                  )}
                >
                  <span>{status.label}</span>
                  {status.count > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {status.count}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>

          {/* Assigned to Me Toggle */}
          <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2">
            <User className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Assigned to me</span>
            <Switch
              checked={filters.assigned_to_me}
              onCheckedChange={handleAssignedToMeToggle}
              className="ml-2"
            />
          </div>

          {/* Favourites Only Toggle */}
          <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2">
            <span className="text-yellow-500 text-lg">★</span>
            <span className="text-sm font-medium text-gray-700">Favourites only</span>
            <Switch
              checked={favouritesOnly}
              onCheckedChange={handleFavouritesOnlyToggle}
              className="ml-2"
            />
          </div>

          {/* Date Filter */}
          <Popover open={showDatePicker} onOpenChange={setShowDatePicker}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "gap-2",
                  dateRange ? "bg-blue-50 border-blue-200 text-blue-700" : ""
                )}
              >
                <CalendarIcon className="h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  "Date range"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          {/* Clear Date Range */}
          {dateRange && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDateRangeChange(undefined)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Sort and Actions Row */}
        <div className="flex items-center justify-between">
          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                {filters.sort_dir === 'asc' ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
                {SORT_OPTIONS.find(opt => opt.value === filters.sort_by)?.label || 'Sort by'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {SORT_OPTIONS.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleSortChange(option.value, 'desc')}
                  className={cn(
                    "flex items-center gap-2",
                    filters.sort_by === option.value && filters.sort_dir === 'desc' && "bg-blue-50"
                  )}
                >
                  <SortDesc className="h-4 w-4" />
                  {option.label} (Newest)
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              {SORT_OPTIONS.map((option) => (
                <DropdownMenuItem
                  key={`${option.value}-asc`}
                  onClick={() => handleSortChange(option.value, 'asc')}
                  className={cn(
                    "flex items-center gap-2",
                    filters.sort_by === option.value && filters.sort_dir === 'asc' && "bg-blue-50"
                  )}
                >
                  <SortAsc className="h-4 w-4" />
                  {option.label} (Oldest)
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {/* Save Preferences */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSavePreferences}
              disabled={savingPreferences}
              className="gap-2"
            >
              <Settings className="h-4 w-4" />
              Save as Default
            </Button>

            {/* Reset Filters */}
            {hasCustomFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetFilters}
                className="gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Stats Summary */}
      <motion.div
        className="flex items-center gap-4 text-base text-gray-500"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <span className="font-medium">
          {loading ? 'Loading...' : `${totalDeals} deals total`}
        </span>
        {hasCustomFilters && (
          <Badge variant="secondary" className="bg-blue-50 text-blue-700">
            Filters applied
          </Badge>
        )}
      </motion.div>
    </motion.div>
  );
}
