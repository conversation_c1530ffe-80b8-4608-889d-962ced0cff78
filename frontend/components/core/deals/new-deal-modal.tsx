"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader2, Upload, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { PitchUploadAPI } from "@/lib/api/pitch-upload-api";

interface NewDealModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (data: any) => Promise<void>; // Optional for backward compatibility
}

interface UploadState {
  file: File | null;
  progress: number;
  status: 'idle' | 'preparing' | 'uploading' | 'processing' | 'completed' | 'error';
  error: string | null;
}

export function NewDealModal({ open, onOpenChange, onSubmit }: NewDealModalProps) {
  const [uploadState, setUploadState] = React.useState<UploadState>({
    file: null,
    progress: 0,
    status: 'idle',
    error: null,
  });
  const [isDragOver, setIsDragOver] = React.useState(false);

  // Debug logs
  console.log('🔍 NewDealModal Debug:', {
    open,
    uploadState,
    isDragOver
  });

  // Reset state when modal opens/closes
  React.useEffect(() => {
    console.log('🔄 Modal open state changed:', open);
    if (open) {
      setUploadState({
        file: null,
        progress: 0,
        status: 'idle',
        error: null,
      });
      setIsDragOver(false);
      console.log('✅ Upload state reset on modal open');
    }
  }, [open]);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (file.type !== 'application/pdf') {
      return 'Only PDF files are allowed for pitch decks.';
    }

    // Check file size (50MB max)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return 'File too large. Maximum size is 50MB.';
    }

    return null;
  };

  const handleFileSelect = (file: File) => {
    console.log('📁 File selected:', { name: file.name, size: file.size, type: file.type });
    
    const error = validateFile(file);
    if (error) {
      setUploadState(prev => ({ ...prev, error, status: 'error' }));
      return;
    }

    setUploadState({
      file,
      progress: 0,
      status: 'idle',
      error: null,
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('📁 File input change event triggered');
    console.log('📁 Files:', e.target.files);
    
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log('📁 Selected file:', files[0]);
      handleFileSelect(files[0]);
    } else {
      console.log('📁 No files selected');
    }
  };

  const handleUpload = async () => {
    if (!uploadState.file) return;

    console.log('🚀 Starting pitch upload:', uploadState.file.name);
    
    setUploadState(prev => ({ ...prev, status: 'preparing', error: null }));

    try {
      // Get auth tokens
      const token = localStorage.getItem('token');
      const orgId = localStorage.getItem('orgId');

      if (!token || !orgId) {
        throw new Error('Authentication required. Please log in again.');
      }

      // Upload the pitch deck
      await PitchUploadAPI.uploadPitchComplete(
        uploadState.file,
        token,
        orgId,
        (progress, status) => {
          console.log('📊 Upload progress:', { progress, status });
          setUploadState(prev => ({ ...prev, progress, status: status as any }));
        }
      );

      console.log('✅ Pitch upload completed successfully');
      
      // Close modal after successful upload
      setTimeout(() => {
        onOpenChange(false);
      }, 2000); // Give user time to see success message

    } catch (err: any) {
      console.error('❌ Pitch upload failed:', err);
      setUploadState(prev => ({ 
        ...prev, 
        status: 'error', 
        error: err?.message || "Failed to upload pitch deck. Please try again." 
      }));
    }
  };

  const getStatusMessage = () => {
    switch (uploadState.status) {
      case 'preparing':
        return 'Preparing upload...';
      case 'uploading':
        return `Uploading... ${Math.round(uploadState.progress)}%`;
      case 'processing':
        return 'Processing pitch deck...';
      case 'completed':
        return 'Pitch deck uploaded successfully! Deal will be created automatically.';
      case 'error':
        return uploadState.error || 'Upload failed';
      default:
        return 'Drag and drop a PDF pitch deck here, or click to browse';
    }
  };

  const getStatusIcon = () => {
    switch (uploadState.status) {
      case 'preparing':
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-8 w-8 text-red-500" />;
      default:
        return <Upload className="h-8 w-8 text-gray-400" />;
    }
  };

  const isUploading = ['preparing', 'uploading', 'processing'].includes(uploadState.status);
  const isCompleted = uploadState.status === 'completed';
  const isError = uploadState.status === 'error';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[480px] w-full p-0">
        <DialogHeader className="px-6 pt-6">
          <DialogTitle className="text-xl font-semibold">Upload Pitch Deck</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Upload a PDF pitch deck to automatically create a deal. Our AI will extract company information and create a structured deal record.
          </DialogDescription>
        </DialogHeader>
        
        <div className="px-6 pt-4 pb-6 space-y-5">
          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragOver 
                ? 'border-blue-500 bg-blue-50' 
                : isError 
                ? 'border-red-300 bg-red-50' 
                : isCompleted 
                ? 'border-green-300 bg-green-50' 
                : 'border-gray-300 bg-gray-50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center space-y-4">
              {getStatusIcon()}
              
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-900">
                  {uploadState.file ? uploadState.file.name : 'Upload Pitch Deck'}
                </p>
                <p className="text-xs text-gray-500">
                  {getStatusMessage()}
                </p>
              </div>

              {/* Progress Bar */}
              {isUploading && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadState.progress}%` }}
                  />
                </div>
              )}

              {/* File Selection Button */}
              <div className="space-y-2">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handleFileInputChange}
                  className="hidden"
                  id="pitch-file-input"
                />
                <label htmlFor="pitch-file-input">
                  <Button 
                    variant="outline" 
                    className="cursor-pointer"
                    disabled={isUploading}
                    onClick={() => {
                      console.log('🔍 Browse button clicked');
                      const fileInput = document.getElementById('pitch-file-input') as HTMLInputElement;
                      if (fileInput) {
                        console.log('📁 Triggering file input click');
                        fileInput.click();
                      } else {
                        console.error('❌ File input not found');
                      }
                    }}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    {uploadState.file ? 'Change File' : 'Browse Files'}
                  </Button>
                </label>
              </div>

              {/* File Info */}
              {uploadState.file && (
                <div className="text-xs text-gray-500 space-y-1">
                  <p>File: {uploadState.file.name}</p>
                  <p>Size: {(uploadState.file.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
              )}
            </div>
          </div>

          {/* Error Message */}
          {isError && uploadState.error && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {uploadState.error}
            </div>
          )}

          {/* Success Message */}
          {isCompleted && (
            <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md">
              Your pitch deck has been uploaded successfully! The AI is now processing it and will create a deal automatically. You can close this window.
            </div>
          )}

          <DialogFooter className="pt-4 flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUploading}
              className="min-w-[100px]"
            >
              {isCompleted ? 'Close' : 'Cancel'}
            </Button>
            
            {!isCompleted && (
              <Button
                type="button"
                onClick={handleUpload}
                disabled={!uploadState.file || isUploading}
                className="min-w-[140px]"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    {uploadState.status === 'preparing' ? 'Preparing...' :
                     uploadState.status === 'uploading' ? 'Uploading...' :
                     'Processing...'}
                  </>
                ) : (
                  'Upload & Process'
                )}
              </Button>
            )}
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
